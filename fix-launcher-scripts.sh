#!/bin/bash
# 文件: my-app-packaging/fix-launcher-scripts.sh
# 用途: 修复已生成的启动脚本中的硬编码路径问题
# 说明: 将构建时的绝对路径替换为运行时的相对路径

set -euo pipefail

echo "🔧 修复启动脚本中的路径问题"
echo "================================"

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PACKAGES_DIR="$SCRIPT_DIR/packages"
PACKAGE_ID="com.ifieldmaster.core"
PACKAGE_DATA_DIR="$PACKAGES_DIR/$PACKAGE_ID/data"

if [ ! -d "$PACKAGE_DATA_DIR/bin" ]; then
  echo "❌ 错误: 未找到包数据目录: $PACKAGE_DATA_DIR/bin"
  echo "请先运行 build-installer.sh 生成安装包"
  exit 1
fi

# 统计和修复
FIXED_COUNT=0
TOTAL_COUNT=0

echo "检查并修复启动脚本..."
for script in "$PACKAGE_DATA_DIR/bin"/*.sh; do
  if [ -f "$script" ]; then
    TOTAL_COUNT=$((TOTAL_COUNT + 1))
    script_name="$(basename "$script")"
    
    # 检查是否包含构建时的绝对路径
    if grep -q "/home/<USER>/build-installer.sh" "$script" 2>/dev/null; then
      echo "🔧 修复脚本: $script_name"
      
      # 获取可执行文件名（去掉.sh后缀）
      exe_name="${script_name%.sh}"
      
      # 重新生成正确的启动脚本
      cat > "$script" <<'EOF'
#!/bin/bash
# 获取脚本所在目录的绝对路径
HERE="$(cd "$(dirname "$0")" && pwd)"
# 获取安装根目录（bin的上级目录）
ROOT="$(cd "$HERE/.." && pwd)"

# 设置库路径 - 优先使用包内库，然后使用系统库
export LD_LIBRARY_PATH="$ROOT/lib:$HERE:${LD_LIBRARY_PATH:-}"

# 设置 Qt 插件路径
export QT_PLUGIN_PATH="$ROOT/plugins"
export QT_QPA_PLATFORM_PLUGIN_PATH="$ROOT/plugins/platforms"

# 设置其他插件路径
export GST_PLUGIN_PATH="$ROOT/plugins/gstreamer-1.0"

# 可选：启用 Qt 插件调试（生产环境可注释掉）
# export QT_DEBUG_PLUGINS=1

# 运行程序，传递所有参数
EOF
      # 添加 exec 行
      echo "exec \"\$HERE/$exe_name\" \"\$@\"" >> "$script"
      
      # 确保可执行权限
      chmod +x "$script"
      
      FIXED_COUNT=$((FIXED_COUNT + 1))
      echo "  ✅ 已修复: $script_name"
    else
      echo "  ✅ 无需修复: $script_name"
    fi
  fi
done

echo ""
echo "📊 修复结果统计:"
echo "  总启动脚本数: $TOTAL_COUNT"
echo "  已修复脚本数: $FIXED_COUNT"
echo "  无需修复数: $((TOTAL_COUNT - FIXED_COUNT))"

if [ $FIXED_COUNT -gt 0 ]; then
  echo ""
  echo "🎉 启动脚本修复完成!"
  echo "现在可以重新构建安装程序:"
  echo "  $SCRIPT_DIR/build-installer.sh"
else
  echo ""
  echo "✅ 所有启动脚本都是正确的，无需修复"
fi

echo ""
echo "💡 验证方法:"
echo "  可以检查任意 .sh 文件，确保 HERE 变量使用相对路径:"
echo "  cat $PACKAGE_DATA_DIR/bin/FMView.sh"
