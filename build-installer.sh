#!/bin/bash
# 文件: my-app-packaging/build-installer.sh
# 用途: 生成 AppDir 并用 Qt Installer Framework 打离线安装包
# 说明: 先用 linuxdeployqt 收集 Qt/第三方依赖，后用 binarycreator 生成安装器

set -euo pipefail

echo "开始构建安装程序..."

# 1) 路径与变量（自动检测同级目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"                   # 工程根（自动检测）
BIN_DIR="$ROOT/bin"                                    # 所有可执行文件
LIB_DIR="$ROOT/lib"                                    # 你自带的 .so 所在目录
TOOLS_DIR="$SCRIPT_DIR/tools"                          # 下载工具放这里
APPDIR="$SCRIPT_DIR/AppDir"                            # linuxdeployqt 输出目录
CONFIG_DIR="$SCRIPT_DIR/config"                        # Qt IFW 配置
PACKAGES_DIR="$SCRIPT_DIR/packages"                    # Qt IFW 包目录
OUTPUT_FILE="$SCRIPT_DIR/ifieldmaster-installer"       # 安装器输出文件
PACKAGE_ID="com.ifieldmaster.core"                     # 你的包ID（按需修改）
PACKAGE_DATA_DIR="$PACKAGES_DIR/$PACKAGE_ID/data"      # 安装包数据目录
USE_LINUXDEPLOY="${USE_LINUXDEPLOY:-0}"                # 是否使用 linuxdeploy 补扫（默认关闭）

# 2) 前置校验（Qt IFW）
if ! command -v binarycreator &> /dev/null; then
    echo "错误: 找不到 binarycreator 工具"
    echo "请安装 Qt Installer Framework 并加入 PATH"
    echo "下载: https://download.qt.io/official_releases/qt-installer-framework/"
    exit 1
fi

# 3) 准备工具: linuxdeploy / linuxdeployqt（若无则自动下载）
mkdir -p "$TOOLS_DIR"
LINUXDEPLOY="$TOOLS_DIR/linuxdeploy-x86_64.AppImage"
LINUXDEPLOYQT="$TOOLS_DIR/linuxdeployqt-x86_64.AppImage"

download_if_missing() {
  local url="$1"
  local dst="$2"
  if [ ! -f "$dst" ]; then
    echo "下载 $dst ..."
    wget -O "$dst" "$url"
    chmod +x "$dst"
  fi
}

download_if_missing "https://github.com/linuxdeploy/linuxdeploy/releases/download/continuous/linuxdeploy-x86_64.AppImage" "$LINUXDEPLOY"
download_if_missing "https://github.com/probonopd/linuxdeployqt/releases/download/continuous/linuxdeployqt-x86_64.AppImage" "$LINUXDEPLOYQT"

# 4) 基本目录校验
if [ ! -d "$BIN_DIR" ] || [ -z "$(find "$BIN_DIR" -maxdepth 1 -type f -executable -printf '.\n' | head -n1)" ]; then
  echo "错误: 未找到可执行文件，请先在 $BIN_DIR 构建你的程序"
  exit 1
fi

if [ ! -d "$PACKAGES_DIR" ]; then
  echo "错误: 包目录不存在: $PACKAGES_DIR"
  exit 1
fi

if [ ! -f "$CONFIG_DIR/config.xml" ]; then
  echo "错误: 配置文件不存在: $CONFIG_DIR/config.xml"
  exit 1
fi

# 5) 清理旧输出
if [ -e "$OUTPUT_FILE" ]; then
  if [ -d "$OUTPUT_FILE" ]; then
    rm -rf "$OUTPUT_FILE"
  else
    rm -f "$OUTPUT_FILE"
  fi
fi
rm -rf "$APPDIR" || true
mkdir -p "$APPDIR"

# 6) 选择主执行文件（用于 linuxdeployqt 的入口）
#    优先 FMProcManager → FMView → FMDataServer，否则取 bin 下第一个可执行文件（仅顶层）
BIN_SEARCH_DEPTH="${BIN_SEARCH_DEPTH:-1}"
MAIN_EXE=""
find_main_by_name() {
  local name="$1"
  find "$BIN_DIR" -maxdepth "$BIN_SEARCH_DEPTH" -type f -name "$name" -executable 2>/dev/null | head -n1
}
MAIN_EXE="$(find_main_by_name FMProcManager)"
if [ -z "$MAIN_EXE" ]; then MAIN_EXE="$(find_main_by_name FMView)"; fi
if [ -z "$MAIN_EXE" ]; then MAIN_EXE="$(find_main_by_name FMDataServer)"; fi
if [ -z "$MAIN_EXE" ]; then
  MAIN_EXE="$(find "$BIN_DIR" -maxdepth "$BIN_SEARCH_DEPTH" \( -type f -o -type l \) -print0 2>/dev/null | while IFS= read -r -d '' f; do [ -x "$f" ] && echo "$f"; done | head -n1)"
fi
if [ -z "$MAIN_EXE" ]; then
  echo "错误: 未找到主执行文件"
  exit 1
fi
echo "主执行文件: $MAIN_EXE"

# 7) 构造 linuxdeployqt 命令：主可执行 + 其它可执行作为附加（递归、支持符号链接）
EXTRA_EXE_ARGS=()
ALL_EXECUTABLES=()
while IFS= read -r -d '' file; do
  if [ -x "$file" ]; then
    ALL_EXECUTABLES+=("$file")
  fi
done < <(find "$BIN_DIR" -maxdepth "$BIN_SEARCH_DEPTH" \( -type f -o -type l \) -print0 2>/dev/null)

for exe in "${ALL_EXECUTABLES[@]}"; do
  [ "$exe" = "$MAIN_EXE" ] && continue
  EXTRA_EXE_ARGS+=("-executable=$exe")
done
echo "发现可执行文件数量: ${#ALL_EXECUTABLES[@]}（含主入口）"

# 8) 运行 linuxdeployqt，生成 AppDir
#    -bundle-non-qt-libs 打包非 Qt 依赖
#    -no-translations 避免塞入大量翻译
#    -appdir 指定输出
echo "运行 linuxdeployqt 收集依赖到 AppDir ..."
"$LINUXDEPLOYQT" "$MAIN_EXE" \
  -bundle-non-qt-libs \
  -no-translations \
  -appdir "$APPDIR" \
  "${EXTRA_EXE_ARGS[@]}"

# 8.1) 检查是否成功生成可执行文件
if [ ! -f "$APPDIR/usr/bin/$(basename "$MAIN_EXE")" ]; then
  echo "错误: linuxdeployqt 未能成功生成可执行文件"
  echo "尝试手动复制可执行文件..."
  mkdir -p "$APPDIR/usr/bin"
  cp "$MAIN_EXE" "$APPDIR/usr/bin/"
fi

# 8.1) 校验并强制补齐常用 Qt 插件目录（platforms 等），避免 xcb 缺失
ensure_qt_plugins() {
  local need=(platforms imageformats iconengines styles xcbglintegrations platformthemes)
  local dst_root="$APPDIR/usr/plugins"
  mkdir -p "$dst_root"

  # 如果 linuxdeployqt 已经生成 platforms/libqxcb.so 则直接返回
  if [ -f "$dst_root/platforms/libqxcb.so" ]; then
    return 0
  fi

  # 自动探测构建机的 Qt 插件目录
  local candidates=()
  if [ -n "${QT_PLUGIN_DIR_HINT:-}" ]; then candidates+=("$QT_PLUGIN_DIR_HINT"); fi
  local qmake_plug
  qmake_plug="$(qmake -query QT_INSTALL_PLUGINS 2>/dev/null || true)"
  if [ -n "$qmake_plug" ] && [ -d "$qmake_plug" ]; then candidates+=("$qmake_plug"); fi
  # 常见路径
  for p in \
    "$HOME/Qt/5.15.2/gcc_64/plugins" \
    "/usr/lib64/qt5/plugins" \
    "/usr/lib/x86_64-linux-gnu/qt5/plugins" \
    "/usr/lib/qt/plugins"; do
    [ -d "$p" ] && candidates+=("$p")
  done

  for src in "${candidates[@]}"; do
    if [ -f "$src/platforms/libqxcb.so" ]; then
      echo "从 $src 补齐 Qt 插件到 $dst_root ..."
      for d in "${need[@]}"; do
        if [ -d "$src/$d" ]; then
          mkdir -p "$dst_root/$d"
          cp -a "$src/$d"/. "$dst_root/$d/"
        fi
      done
      break
    fi
  done

  if [ ! -f "$dst_root/platforms/libqxcb.so" ]; then
    echo "⚠️  警告: 仍未找到 libqxcb.so，请检查构建机 Qt 安装并设置 QT_PLUGIN_DIR_HINT"
  fi
}
ensure_qt_plugins

# 9) 合并你项目自带的第三方 .so 到 AppDir/usr/lib
mkdir -p "$APPDIR/usr/lib"
if [ -d "$LIB_DIR" ]; then
  echo "同步自带库到 AppDir/usr/lib ..."
  find "$LIB_DIR" -maxdepth 1 -type f -name "*.so*" -exec cp -f {} "$APPDIR/usr/lib/" \;
fi

# 10.1) 同步 bin 下的配置文件/目录到 AppDir/usr/bin（可扩展）
#       通过环境变量控制：
#         - BIN_CONFIG_ENABLE=0 关闭本步骤
#         - BIN_CONFIG_FILE_EXTS="ini conf json cfg yaml yml toml xml properties" 自定义扩展名
#         - BIN_CONFIG_DIR_PATTERNS="Setting settings config configs resources res assets etc" 自定义顶层目录名
BIN_CONFIG_ENABLE=${BIN_CONFIG_ENABLE:-1}
BIN_CONFIG_FILE_EXTS_STR=${BIN_CONFIG_FILE_EXTS:-"ini conf json cfg yaml yml toml xml properties"}
BIN_CONFIG_DIR_PATTERNS=${BIN_CONFIG_DIR_PATTERNS:-"Setting settings config configs resources res assets etc"}
if [ "$BIN_CONFIG_ENABLE" = "1" ]; then
  echo "同步 bin 配置到 AppDir/usr/bin ..."
  mkdir -p "$APPDIR/usr/bin"
  # 顶层常见配置目录
  for d in $BIN_CONFIG_DIR_PATTERNS; do
    if [ -d "$BIN_DIR/$d" ]; then
      cp -a "$BIN_DIR/$d" "$APPDIR/usr/bin/"
    fi
  done
  # 顶层常见配置文件（按扩展名）
  FIND_EXPR=()
  for ext in $BIN_CONFIG_FILE_EXTS_STR; do
    FIND_EXPR+=( -name "*.$ext" -o )
  done
  if [ ${#FIND_EXPR[@]} -gt 0 ]; then
    unset 'FIND_EXPR[${#FIND_EXPR[@]}-1]'
    find "$BIN_DIR" -maxdepth 1 -type f \( "${FIND_EXPR[@]}" \) -exec cp -f {} "$APPDIR/usr/bin/" \;
  fi
else
  echo "跳过同步 bin 配置（BIN_CONFIG_ENABLE=0）"
fi

# 10.2) 兜底：如有可执行未被 linuxdeployqt 放入 AppDir/usr/bin，则补拷（保留相对子路径）
echo "检查并补充遗漏的可执行文件 ..."
for exe in "${ALL_EXECUTABLES[@]}"; do
  rel="${exe#"$BIN_DIR"/}"
  base="$(basename "$exe")"
  if [ ! -e "$APPDIR/usr/bin/$base" ] && [ ! -e "$APPDIR/usr/bin/$rel" ]; then
    mkdir -p "$APPDIR/usr/bin/$(dirname "$rel")"
    cp -a "$exe" "$APPDIR/usr/bin/$rel"
    echo "  已补拷: $rel"
  fi
done

# 10.3) 为可执行生成 .sh 启动脚本（不改动原可执行文件名）
echo "生成 .sh 启动脚本 ..."
for exe in "${ALL_EXECUTABLES[@]}"; do
  rel="${exe#"$BIN_DIR"/}"
  name="$(basename "$rel")"
  realbin="$APPDIR/usr/bin/$name"
  launchersh="$APPDIR/usr/bin/$name.sh"
  if [ -f "$realbin" ] && file -b "$realbin" | grep -qi "executable"; then
    # 使用 cat 和 EOF 来避免变量展开问题
    cat > "$launchersh" <<'EOF'
#!/bin/bash
# 获取脚本所在目录的绝对路径
HERE="$(cd "$(dirname "$0")" && pwd)"
# 获取安装根目录（bin的上级目录）
ROOT="$(cd "$HERE/.." && pwd)"

# 设置库路径 - 优先使用包内库，然后使用系统库
export LD_LIBRARY_PATH="$ROOT/lib:$HERE:${LD_LIBRARY_PATH:-}"

# 设置 Qt 插件路径
export QT_PLUGIN_PATH="$ROOT/plugins"
export QT_QPA_PLATFORM_PLUGIN_PATH="$ROOT/plugins/platforms"

# 设置其他插件路径
export GST_PLUGIN_PATH="$ROOT/plugins/gstreamer-1.0"

# 可选：启用 Qt 插件调试（生产环境可注释掉）
# export QT_DEBUG_PLUGINS=1

# 运行程序，传递所有参数
EOF
    # 单独添加 exec 行，使用变量替换
    echo "exec \"\$HERE/$name\" \"\$@\"" >> "$launchersh"
    chmod +x "$launchersh"
    echo "  已生成启动脚本: $name.sh"
  fi
done

# 10) 可选：拷贝 GCC 运行库（避免 GLIBCXX_* 问题），存在才复制
# 尝试多个可能的GCC路径
GCC_PATHS=("/usr/local/gcc-11.2.0/lib64" "/usr/local/gcc-11/lib64" "/usr/local/gcc/lib64" "/usr/lib/gcc" "/usr/lib64")
GCC_FOUND=false

for gcc_path in "${GCC_PATHS[@]}"; do
  if [ -d "$gcc_path" ]; then
    echo "发现 GCC 路径: $gcc_path"
    # 查找 libstdc++.so.6 和 libgcc_s.so.1
    for f in libstdc++.so.6 libgcc_s.so.1; do
      # 递归查找库文件
      found_lib=$(find "$gcc_path" -name "$f" -type f 2>/dev/null | head -n1)
      if [ -n "$found_lib" ]; then
        echo "同步 $f 到 AppDir/usr/lib ..."
        cp -f "$found_lib" "$APPDIR/usr/lib/" || true
        GCC_FOUND=true
      fi
    done
  fi
done

if [ "$GCC_FOUND" = false ]; then
  echo "警告: 未找到 GCC 运行库，可能导致 GLIBCXX_* 问题"
fi

# 11) 写入 qt.conf，明确前缀与插件/库路径（适配扁平化后的 bin/lib/plugins）
mkdir -p "$APPDIR/usr/bin"
cat > "$APPDIR/usr/bin/qt.conf" <<'EOF'
[Paths]
Prefix=..
Plugins=plugins
Libraries=lib
EOF

# 12) 可选：为可执行文件设置 RPATH=$ORIGIN/../lib（如果系统有 patchelf）
if command -v patchelf >/dev/null 2>&1; then
  echo "设置 RPATH 到 $ORIGIN/../lib ..."
  find "$APPDIR/usr/bin" -maxdepth 1 -type f -executable -print0 | while IFS= read -r -d '' exe; do
    patchelf --set-rpath '$ORIGIN/../lib' "$exe" || true
  done
  # 若你有自研 .so 也需要相对寻址
  find "$APPDIR/usr/lib" -type f -name "*.so*" -print0 | while IFS= read -r -d '' so; do
    patchelf --set-rpath '$ORIGIN' "$so" || true
  done
  # 为 Qt 插件设置 RPATH，确保能解析到安装根 lib
  if [ -d "$APPDIR/usr/plugins" ]; then
    echo "设置 Qt 插件 RPATH 到 $ORIGIN/../../lib ..."
    find "$APPDIR/usr/plugins" -type f -name "*.so*" -print0 | while IFS= read -r -d '' plug; do
      patchelf --set-rpath '$ORIGIN/../../lib' "$plug" || true
    done
  fi
else
  echo "提示: 未发现 patchelf，跳过设置 RPATH（通常也可由 linuxdeployqt 的启动器处理）"
fi

# 13) 用 linuxdeploy 再扫一遍 AppDir，补齐链式依赖（可选）
if [ "$USE_LINUXDEPLOY" = "1" ]; then
  echo "用 linuxdeploy 补齐链式依赖 ..."
  "$LINUXDEPLOY" --appdir "$APPDIR" --output appdir >/dev/null 2>&1 || true
else
  echo "跳过 linuxdeploy 补齐（USE_LINUXDEPLOY=0）"
fi

# 14) 扁平化同步 AppDir/usr 到 Qt IFW 包 data 目录（安装根不再出现 usr/）
echo "同步 AppDir/usr 到安装包数据目录(扁平化): $PACKAGE_DATA_DIR"
rm -rf "$PACKAGE_DATA_DIR"
mkdir -p "$PACKAGE_DATA_DIR"

# 创建目标子目录
mkdir -p "$PACKAGE_DATA_DIR/bin" "$PACKAGE_DATA_DIR/lib" "$PACKAGE_DATA_DIR/plugins"

# 复制 bin/lib/plugins
if [ -d "$APPDIR/usr/bin" ]; then
  cp -a "$APPDIR/usr/bin"/. "$PACKAGE_DATA_DIR/bin/"
fi
if [ -d "$APPDIR/usr/lib" ]; then
  cp -a "$APPDIR/usr/lib"/. "$PACKAGE_DATA_DIR/lib/"
fi
if [ -d "$APPDIR/usr/plugins" ]; then
  cp -a "$APPDIR/usr/plugins"/. "$PACKAGE_DATA_DIR/plugins/"
fi

# 14.2) 验证生成的启动脚本是否正确
echo "验证生成的启动脚本..."
SCRIPT_VALIDATION_FAILED=false
for script in "$PACKAGE_DATA_DIR/bin"/*.sh; do
  if [ -f "$script" ]; then
    # 检查是否包含构建时的绝对路径
    if grep -q "/home/<USER>" "$script" 2>/dev/null; then
      echo "❌ 警告: 发现启动脚本包含构建时路径: $(basename "$script")"
      SCRIPT_VALIDATION_FAILED=true
    else
      echo "✅ 启动脚本验证通过: $(basename "$script")"
    fi
  fi
done

if [ "$SCRIPT_VALIDATION_FAILED" = true ]; then
  echo "⚠️  检测到启动脚本问题，但继续构建..."
fi

# 14.1) 确保组件元信息存在（若缺失则自动创建最小 package.xml）
PACKAGE_META_DIR="$PACKAGES_DIR/$PACKAGE_ID/meta"
if [ ! -d "$PACKAGE_META_DIR" ]; then
  mkdir -p "$PACKAGE_META_DIR"
fi
if [ ! -f "$PACKAGE_META_DIR/package.xml" ]; then
  RELEASE_DATE="$(date +%F)"
  cat > "$PACKAGE_META_DIR/package.xml" <<EOF
<?xml version="1.0" encoding="UTF-8"?>
<Package>
  <DisplayName>iFieldMaster Core</DisplayName>
  <Description>Core binaries and libraries</Description>
  <Version>1.0.0</Version>
  <ReleaseDate>$RELEASE_DATE</ReleaseDate>
  <Name>$PACKAGE_ID</Name>
  <Default>true</Default>
  <ForcedInstallation>true</ForcedInstallation>
  <RequiresAdminRights>false</RequiresAdminRights>
</Package>
EOF
fi

# 15) 调用 binarycreator 生成离线安装包
echo "正在构建安装程序..."
echo "配置目录: $CONFIG_DIR"
echo "包目录: $PACKAGES_DIR"
echo "输出文件: $OUTPUT_FILE"

binarycreator \
  --offline-only \
  -c "$CONFIG_DIR/config.xml" \
  -p "$PACKAGES_DIR" \
  "$OUTPUT_FILE"

if [ -f "$OUTPUT_FILE" ]; then
  echo "✅ 安装程序构建成功!"
  echo "输出文件: $OUTPUT_FILE"
  echo "文件大小: $(du -h "$OUTPUT_FILE" | cut -f1)"
  chmod +x "$OUTPUT_FILE"
  echo "已设置可执行权限"
  echo ""
  echo "使用方法:"
  echo "  $OUTPUT_FILE    # 运行安装程序"
else
  echo "❌ 安装程序构建失败!"
  exit 1
fi